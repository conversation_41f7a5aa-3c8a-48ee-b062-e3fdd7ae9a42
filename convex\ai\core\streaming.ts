"use node";

import { streamText } from "ai";
import { api, internal } from "../../_generated/api";
import { createAvailableTools } from "../tools";
import {
  createAIModel,
  getProviderApiKey,
  getDefaultModel,
  getModelInfo,
  SupportedProvider,
} from "../providers";
import { PROVIDER_BASE_URLS } from "../providers/constants";
import { getUserFriendlyErrorMessage } from "../utils/errors";

// Allow streaming responses up to 10 minutes (600 seconds) for long-running models like Gemini 2.5 Pro
export const maxDuration = 600;

export async function generateStreamingResponse(
  ctx: any,
  args: {
    conversationId: string;
    branchId?: string;
    messages: Array<{
      role: string;
      content:
        | string
        | Array<{
            type: "text" | "image" | "file";
            text?: string;
            image?: string;
            file?: string;
            data?: string;
          }>;
    }>;
    provider?: string;
    model?: string;
    temperature?: number;
    enabledTools?: string[];
    thinkingBudget?: string | number;
    persona?: string;
    recipe?: string;
  }
) {
  const provider = (args.provider as SupportedProvider) || "openai";
  const model = args.model ?? getDefaultModel(provider);
  const temperature = args.temperature ?? 1;
  const enabledTools = args.enabledTools ?? [];
  const branchId = args.branchId;
  const personaId = args.persona || "none";
  const recipeId = args.recipe || "none";

  // Check if this is a custom provider
  let customProvider = null;
  if (
    typeof args.provider === "string" &&
    ![
      "openai",
      "anthropic",
      "google",
      "openrouter",
      "groq",
      "deepseek",
      "grok",
      "cohere",
      "mistral",
      "cerebras",
      "github",
    ].includes(args.provider)
  ) {
    // This is a custom provider, fetch its configuration
    customProvider = await ctx.runQuery(api.customProviders.getByName, {
      name: args.provider,
    });
    if (!customProvider) {
      throw new Error(`Custom provider '${args.provider}' not found`);
    }
  }

  let messageId: any = null;
  let usingUserKey = false;
  let abortController: AbortController | null = null;
  let cancellationCheckInterval: NodeJS.Timeout | null = null;

  try {
    // Create an AbortController for timeout handling
    abortController = new AbortController();

    // Set timeout based on provider - Gemini models need longer timeouts
    const timeoutMs = provider === "google" ? 600000 : 300000; // 10 min for Google, 5 min for others
    const timeoutId = setTimeout(() => {
      if (abortController && !abortController.signal.aborted) {
        abortController.abort(
          new Error(`Request timeout after ${timeoutMs / 1000} seconds`)
        );
      }
    }, timeoutMs);

    // Clear any existing cancellation flag to start fresh
    await ctx.runMutation(api.conversations.clearCancellation, {
      conversationId: args.conversationId,
    });

    // Check if the model supports multimodal and if we have image content
    const modelInfo = getModelInfo(model);

    // Convert storage URLs to public URLs for multimodal models
    const processedMessages = await Promise.all(
      args.messages.map(async (message) => {
        if (Array.isArray(message.content)) {
          const processedContent = await Promise.all(
            message.content.map(async (part) => {
              if (part.type === "image") {
                const imageValue = part.image;

                // Check if this is a storage ID (Convex storage IDs are alphanumeric)
                if (imageValue?.match(/^[a-z0-9]{28,}$/)) {
                  try {
                    const publicUrl = await ctx.storage.getUrl(
                      imageValue as any
                    );
                    if (publicUrl) {
                      return {
                        type: "image",
                        image: publicUrl,
                      };
                    } else {
                      console.warn(
                        `Failed to get public URL for storage ID: ${imageValue}`
                      );
                      return part;
                    }
                  } catch (error) {
                    console.error(
                      `Error getting public URL for storage ID ${imageValue}:`,
                      error
                    );
                    return part;
                  }
                }

                return part;
              } else if (part.type === "file") {
                const fileValue = (part as any).file ?? (part as any).data;

                // Check if this is a storage ID
                if (fileValue?.match?.(/^[a-z0-9]{28,}$/)) {
                  try {
                    // Get file metadata using the system table (recommended approach)
                    const fileMetadata = await ctx.db.system.get(fileValue);
                    const publicUrl = await ctx.storage.getUrl(fileValue);

                    if (publicUrl && fileMetadata) {
                      return {
                        type: "file",
                        data: publicUrl,
                        mimeType:
                          fileMetadata.contentType ??
                          "application/octet-stream",
                      };
                    } else {
                      console.warn(
                        `Failed to get public URL or metadata for file storage ID: ${fileValue}`
                      );
                      return part;
                    }
                  } catch (error) {
                    console.error(
                      `Error getting public URL for file storage ID ${fileValue}:`,
                      error
                    );
                    return part;
                  }
                }

                return part;
              }
              return part;
            })
          );
          return { ...message, content: processedContent };
        }
        return message;
      })
    );

    // 1. Get API Key for the selected provider
    let apiKey: string;
    if (customProvider) {
      // For custom providers, use the API key stored in the custom provider configuration
      apiKey = customProvider.apiKey;
      usingUserKey = true;
    } else {
      const apiKeyRecord = await ctx.runQuery(api.apiKeys.getByProvider, {
        provider,
      });

      const { apiKey: retrievedApiKey, usingUserKey: userKeyFlag } =
        getProviderApiKey(provider, apiKeyRecord);
      apiKey = retrievedApiKey;
      usingUserKey = userKeyFlag;
    }

    if (!apiKey) {
      throw new Error(
        `No API key available for ${provider}. Please configure your API key in settings or use a provider with built-in support.`
      );
    }

    // Only check usage limits if using built-in keys
    if (!usingUserKey) {
      // Estimate token usage for credit check (rough estimate)
      const estimatedInputTokens = args.messages.reduce((total, msg) => {
        const content =
          typeof msg.content === "string"
            ? msg.content
            : msg.content
                .map((c) => (c.type === "text" ? (c.text ?? "") : ""))
                .join(" ");
        return total + Math.ceil(content.length / 4); // Rough estimate: 4 chars per token
      }, 0);
      const estimatedOutputTokens = 2000; // Conservative estimate for credit check

      const creditCheck = await ctx.runQuery(api.usage.checkCreditsAvailable, {
        model,
        estimatedInputTokens,
        estimatedOutputTokens,
      });

      if (!creditCheck.hasCredits) {
        throw new Error(
          `Insufficient credits. Required: ${creditCheck.requiredCredits}, Available: ${creditCheck.availableCredits}. Add your own API keys in settings for unlimited usage.`
        );
      }

      if (creditCheck.wouldExceedSpending) {
        throw new Error(
          `This request would exceed your monthly spending limit. Add your own API keys in settings for unlimited usage.`
        );
      }
    }

    // Get MCP servers for the user
    const { getAuthUserId } = await import("@convex-dev/auth/server");
    const userId = await getAuthUserId(ctx);
    let mcpServers: any[] = [];
    let n8nServers: any[] = [];
    let n8nWorkflows: any[] = [];

    if (userId) {
      mcpServers = await ctx.runQuery(api.mcpServers.listEnabled, {});
      // Load server-based n8n configuration
      n8nServers = await ctx.runQuery(api.n8nServers.listEnabled, {});
      // Legacy workflow-based
      n8nWorkflows = await ctx
        .runQuery(api.n8nWorkflows.listEnabled, {})
        .catch(() => []);
    }

    // Create tools based on enabled tools and model capabilities
    const availableTools = await createAvailableTools(
      ctx,
      enabledTools,
      model,
      usingUserKey,
      mcpServers,
      n8nServers,
      n8nWorkflows
    );

    // Check if model supports tools
    const hasTools = Object.keys(availableTools).length > 0;

    // Track generation start time and detailed timing
    const generationStartTime = Date.now();
    let timeToFirstToken: number | undefined;
    let timeToFirstContent: number | undefined;
    let reasoningStartTime: number | undefined;
    let reasoningEndTime: number | undefined;
    let toolExecutionStartTime: number | undefined;
    let toolExecutionEndTime: number | undefined;

    // Create AI model instance with native thinking support
    const { model: streamingAiModel, providerOptions } = createAIModel({
      provider,
      model,
      apiKey,
      baseUrl: customProvider
        ? customProvider.baseURL
        : PROVIDER_BASE_URLS[provider],
      temperature,
      thinkingBudget: args.thinkingBudget,
      customProvider: customProvider
        ? {
            name: customProvider.name,
            baseURL: customProvider.baseURL,
            models: customProvider.models,
          }
        : undefined,
    });

    // Get user preferences and instructions to build system prompt
    const userPreferences = await ctx.runQuery(api.preferences.get);
    const userInstructions = await ctx.runQuery(api.userInstructions.get);

    // Prepare messages with system prompt if enabled
    let messagesWithSystemPrompt = [...processedMessages];

    // Check if there's already a system message
    const hasSystemMessage = processedMessages.some(
      (msg) => msg.role === "system"
    );

    if (!hasSystemMessage) {
      let systemContent = "";

      // Include today's date in the system prompt (YYYY-MM-DD)
      const todayDate = new Date().toISOString().split("T")[0];

      if (
        userPreferences?.useCustomSystemPrompt &&
        userPreferences?.systemPrompt
      ) {
        // Use custom system prompt
        systemContent =
          `Current date: ${todayDate}\n\n` + userPreferences.systemPrompt;
      } else {
        // Use default system prompt when custom is disabled
        systemContent =
          `Current date: ${todayDate}\n\n` +
          "You are ErzenAI, an autonomous AI agent that can reason, plan, and act to accomplish the user's goals. You have access to specialised external tools (e.g. webSearch, calculator, codeAnalysis, imageGeneration, memory, and others) and can decide when and how to combine them.\n\n## Agent Behaviour\n1. Think step-by-step and sketch a short internal plan before answering (use the native thinking channel).\n2. If a tool is helpful, choose the best one, provide precise arguments, and wait for its result.\n3. Reflect on the result, decide whether additional tool calls are needed (you may chain multiple calls), and continue until you have the information required.\n4. Synthesise all insights into a clear, actionable response for the user.\n\n## Tool Guidelines\n- Use tools only when they add value beyond your own knowledge.\n- Chain tool calls when beneficial (e.g. search → calculator → codeAnalysis).\n- Avoid redundant or unnecessary calls; be efficient.\n- Validate and cross-check tool results for consistency.\n\n## Communication Style\n- Friendly, professional, concise yet complete.\n- Ask clarifying questions if the request is ambiguous.\n- Use numbered or bulleted lists for multi-step explanations.\n- Provide a brief summary or next steps when appropriate.\n\nRemember: You are a powerful agent that can seamlessly combine internal reasoning with external tool usage to deliver the best possible outcome.";
      }

      // Append persona prompt if provided
      const PERSONA_PROMPTS: Record<string, string> = {
        companion:
          "You are the user's compassionate companion. Respond with warmth, empathy and encouragement. Prioritise emotional support over facts.",
        friend:
          "You are the user's close friend. Keep the tone casual, supportive and lightly humorous. Use informal language, contractions and emojis when appropriate.",
        comedian:
          "You are a stand-up comedian. Deliver responses with wit and humour while still addressing the user's topic. Make sure jokes are light-hearted and never offensive.",
        not_a_doctor:
          "You are NOT a medical professional. If the user requests medical advice you must disclaim you are not a doctor and encourage consulting a qualified physician. Provide general information only.",
        not_a_therapist:
          "You are NOT a mental-health professional. Provide supportive, non-clinical responses and encourage the user to seek professional help for serious issues.",
      };

      if (personaId && personaId !== "none" && PERSONA_PROMPTS[personaId]) {
        systemContent = PERSONA_PROMPTS[personaId] + "\n\n" + systemContent;
      }

      // Append recipe prompt if provided
      const RECIPE_PROMPTS: Record<string, string> = {
        summarise:
          "Whenever the user provides text, summarise it concisely using bullet-points. If the text is short, provide a one-sentence summary.",
        translate_es:
          "Translate all user input into Spanish. Respond ONLY with the translation, no explanations.",
        brainstorm:
          "Generate a creative list of at least 10 varied ideas that satisfy the user's request. Encourage originality and diversity.",
        email_draft:
          "Craft a professional, well-structured email based on the user's instructions. Use a polite tone and clear formatting.",
      };

      if (recipeId && recipeId !== "none" && RECIPE_PROMPTS[recipeId]) {
        systemContent += "\n\n" + RECIPE_PROMPTS[recipeId];
      }

      // Add user instructions if available
      if (userInstructions && userInstructions.trim()) {
        systemContent +=
          "\n\nAdditional user instructions:\n" + userInstructions;
      }

      // Add system prompt at the beginning
      messagesWithSystemPrompt = [
        {
          role: "system" as const,
          content: systemContent,
        },
        ...processedMessages,
      ];
    }

    // Create an empty assistant message first
    messageId = await ctx.runMutation(api.messages.add, {
      conversationId: args.conversationId,
      branchId: branchId,
      role: "assistant",
      content: "",
    });

    // Set generation state to track ongoing generation
    await ctx.runMutation(api.conversations.setGenerationState, {
      conversationId: args.conversationId,
      isGenerating: true,
      messageId: messageId,
    });

    // Generate the response using streamText - conditionally include tools
    const streamTextConfig: any = {
      model: streamingAiModel,
      messages: messagesWithSystemPrompt as any,
      temperature,
      maxSteps: 25,
      abortSignal: abortController.signal,
      onError: ({ error }: { error: Error }) => {
        console.error(`Streaming error for ${provider}/${model}:`, error);
        // Don't throw here, let it be handled in the catch block
        return `AI model error: ${error.message}`;
      },
      // Include native thinking support via provider options
      providerOptions,
    };

    // Only add tools if model supports them AND we have tools to add
    if (modelInfo.supportsTools && hasTools) {
      streamTextConfig.tools = availableTools;
    }

    const result = await (streamText(streamTextConfig) as any);

    let accumulatedContent = "";
    const toolCalls: any[] = [];
    let totalTokens = 0;
    let promptTokens = 0;
    let completionTokens = 0;
    let actualGenerationTime = 0;
    let accumulatedReasoning: string | undefined = undefined;
    let hasError = false;
    let errorMessage = "";

    // Set up periodic cancellation checking

    const setupCancellationCheck = () => {
      cancellationCheckInterval = setInterval(async () => {
        try {
          const isCancelled = await ctx.runQuery(
            api.conversations.checkCancellation,
            {
              conversationId: args.conversationId,
            }
          );

          if (isCancelled) {
            abortController?.abort(new Error("User cancelled generation"));
            if (cancellationCheckInterval) {
              clearInterval(cancellationCheckInterval);
              cancellationCheckInterval = null;
            }
          }
        } catch (error) {
          console.error("Error in cancellation check:", error);
        }
      }, 500); // Check every 500ms
    };

    setupCancellationCheck();

    // Process the full stream so we can detect tool calls in real time
    for await (const part of result.fullStream) {
      // Check for abort signal
      if (abortController?.signal.aborted) {
        const abortReason =
          abortController.signal.reason?.message || "Request was aborted";

        // Clear the interval
        if (cancellationCheckInterval) {
          clearInterval(cancellationCheckInterval);
          cancellationCheckInterval = null;
        }

        throw new Error(`Stream aborted: ${abortReason}`);
      }

      // Check for cancellation at the start of each iteration
      const isCancelled = await ctx.runQuery(
        api.conversations.checkCancellation,
        {
          conversationId: args.conversationId,
        }
      );

      if (isCancelled) {
        // Clear the interval
        if (cancellationCheckInterval) {
          clearInterval(cancellationCheckInterval);
          cancellationCheckInterval = null;
        }

        abortController?.abort(new Error("User cancelled generation"));

        await ctx.runMutation(api.messages.update, {
          messageId,
          content: accumulatedContent || "Generation was stopped by user.",
          thinking: accumulatedReasoning,
          toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
        });

        // Clear the cancellation flag and generation state
        await ctx.runMutation(api.conversations.clearCancellation, {
          conversationId: args.conversationId,
        });
        await ctx.runMutation(api.conversations.setGenerationState, {
          conversationId: args.conversationId,
          isGenerating: false,
        });

        clearTimeout(timeoutId);
        return {
          messageId,
          content: accumulatedContent || "Generation was stopped by user.",
          usingUserKey,
          generationMetrics: {
            provider,
            model,
            generationTimeMs: Date.now() - generationStartTime,
            temperature,
          },
        };
      }

      const partStartTime = Date.now();

      // Handle error parts in the stream
      if (part.type === "error") {
        hasError = true;
        errorMessage =
          part.error?.message || "Unknown streaming error occurred";
        console.error(`Stream error for ${provider}/${model}:`, part.error);

        // Update message with error content
        await ctx.runMutation(api.messages.update, {
          messageId,
          content: accumulatedContent || `Error: ${errorMessage}`,
          thinking: accumulatedReasoning,
          isError: true,
        });

        // Don't break here - let the stream complete and handle in outer catch
        continue;
      }

      // Text deltas
      if (part.type === "text-delta") {
        const newDelta = part.textDelta ?? "";
        if (newDelta) {
          // Track time to first token if this is the first content
          if (!timeToFirstToken) {
            timeToFirstToken = Date.now() - generationStartTime;
          }
          if (!timeToFirstContent && accumulatedContent.length === 0) {
            timeToFirstContent = Date.now() - generationStartTime;
          }

          accumulatedContent += newDelta;
          completionTokens += Math.ceil(newDelta.length / 4);
        }

        // Update message content incrementally
        await ctx.runMutation(api.messages.update, {
          messageId,
          content: accumulatedContent.trim(),
          thinking: accumulatedReasoning,
        });
      }

      // Reasoning deltas
      if (part.type === "reasoning-delta") {
        const reasoningDelta =
          part.textDelta ?? part.reasoningDelta ?? part.reasoning ?? "";
        if (reasoningDelta) {
          // Track reasoning start time
          if (!reasoningStartTime) {
            reasoningStartTime = Date.now();
          }

          accumulatedReasoning = (accumulatedReasoning || "") + reasoningDelta;

          await ctx.runMutation(api.messages.update, {
            messageId,
            content: accumulatedContent.trim(),
            thinking: accumulatedReasoning,
          });
        }
      }

      // Native thinking from providers (Google, Anthropic, OpenAI)
      if (part.type === "reasoning") {
        const reasoningText = part.textDelta ?? part.reasoning ?? "";
        if (reasoningText) {
          // Track reasoning start time
          if (!reasoningStartTime) {
            reasoningStartTime = Date.now();
          }

          accumulatedReasoning = (accumulatedReasoning || "") + reasoningText;
          await ctx.runMutation(api.messages.update, {
            messageId,
            content: accumulatedContent.trim(),
            thinking: accumulatedReasoning,
          });
        }
      }

      // Handle reasoning finish
      if (part.type === "reasoning-finish") {
        accumulatedReasoning =
          part.text ?? part.reasoning ?? accumulatedReasoning;

        // Track reasoning end time
        if (reasoningStartTime && !reasoningEndTime) {
          reasoningEndTime = Date.now();
        }

        // Update message with final reasoning content
        if (accumulatedReasoning) {
          await ctx.runMutation(api.messages.update, {
            messageId,
            content: accumulatedContent.trim(),
            thinking: accumulatedReasoning,
          });
        }
      }

      // Track token usage if available
      if (part.type === "finish" && part.usage) {
        totalTokens = part.usage.totalTokens ?? totalTokens;
        promptTokens = part.usage.promptTokens ?? promptTokens;
        completionTokens = part.usage.completionTokens ?? completionTokens;
      }

      // Tool calls emitted by the model
      if (part.type === "tool-call") {
        // Track tool execution start time
        if (!toolExecutionStartTime) {
          toolExecutionStartTime = Date.now();
        }

        const toolCall = {
          id: part.toolCallId || `tool_${Date.now()}`,
          name: part.toolName,
          arguments: JSON.stringify(part.args),
          result: undefined,
        };

        toolCalls.push(toolCall);

        // Persist tool call immediately for UI feedback
        await ctx.runMutation(api.messages.update, {
          messageId,
          content: accumulatedContent,
          toolCalls,
        });
      }

      // Tool results
      if (part.type === "tool-result") {
        // Track tool execution end time
        if (toolExecutionStartTime && !toolExecutionEndTime) {
          toolExecutionEndTime = Date.now();
        }

        // Handle thinking tool specially
        if (part.toolName === "thinking") {
          const thinkingText =
            typeof part.result === "string"
              ? part.result
              : JSON.stringify(part.result, null, 2);

          accumulatedReasoning =
            (accumulatedReasoning || "") + `\n${thinkingText}`;

          await ctx.runMutation(api.messages.update, {
            messageId,
            content: accumulatedContent.trim(),
            thinking: accumulatedReasoning,
          });
        }

        // Handle canvas tool specially
        if (part.toolName === "canvas" && part.result) {
          try {
            const canvasData =
              typeof part.result === "string"
                ? JSON.parse(part.result)
                : part.result;

            // Update message with canvas data
            await ctx.runMutation(api.messages.update, {
              messageId,
              content: accumulatedContent.trim(),
              thinking: accumulatedReasoning,
              toolCalls,
              canvasData,
            });
          } catch (error) {
            console.error("Failed to parse canvas data:", error);
          }
        }

        const toolCallIndex = toolCalls.findIndex(
          (call) => call.id === part.toolCallId
        );
        if (toolCallIndex >= 0) {
          toolCalls[toolCallIndex].result = JSON.stringify(part.result);

          await ctx.runMutation(api.messages.update, {
            messageId,
            content: accumulatedContent,
            toolCalls,
          });
        }

        // Check user preferences for separate tool output cards
        const userPreferences = await ctx.runQuery(api.preferences.get);
        if (userPreferences?.showToolOutputs) {
          await ctx.runMutation(api.messages.add, {
            conversationId: args.conversationId,
            branchId: branchId,
            role: "tool",
            content:
              typeof part.result === "string"
                ? part.result
                : JSON.stringify(part.result, null, 2),
            toolCallId: part.toolCallId,
          });
        }
      }

      actualGenerationTime += Date.now() - partStartTime;

      // Brief pause for smooth streaming
      await new Promise((r) => setTimeout(r, 10));
    }

    clearTimeout(timeoutId);

    // If there was an error in the stream, throw it now
    if (hasError) {
      throw new Error(errorMessage || "Stream encountered an error");
    }

    // Clear the cancellation check interval
    if (cancellationCheckInterval) {
      clearInterval(cancellationCheckInterval);
      cancellationCheckInterval = null;
    }

    // Calculate generation metrics with enhanced timing
    const generationEndTime = Date.now();
    const tokensPerSecond =
      completionTokens > 0 && actualGenerationTime > 0
        ? completionTokens / (actualGenerationTime / 1000)
        : 0;

    const generationMetrics = {
      provider,
      model,
      tokensUsed: totalTokens || completionTokens,
      promptTokens: promptTokens || undefined,
      completionTokens: completionTokens || undefined,
      generationTimeMs: actualGenerationTime,
      tokensPerSecond: Math.round(tokensPerSecond * 100) / 100,
      temperature,
      timeToFirstTokenMs: timeToFirstToken,
      timeToFirstContentMs: timeToFirstContent,
      reasoningTimeMs:
        reasoningStartTime && reasoningEndTime
          ? reasoningEndTime - reasoningStartTime
          : undefined,
      toolExecutionTimeMs:
        toolExecutionStartTime && toolExecutionEndTime
          ? toolExecutionEndTime - toolExecutionStartTime
          : undefined,
    };

    // Use the final, resolved values from the SDK for the canonical state
    let finalContent: string;
    let finalThinking: string | undefined;

    try {
      finalContent = (await result.text).trim();
      finalThinking = (await result.reasoning)?.trim() ?? "";
    } catch (resultError) {
      console.error(
        `Error getting final result for ${provider}/${model}:`,
        resultError
      );
      // Fallback to accumulated content if final result fails
      finalContent = accumulatedContent.trim();
      finalThinking = accumulatedReasoning?.trim();
    }

    // Final update to the message
    await ctx.runMutation(api.messages.update, {
      messageId,
      content:
        finalContent ||
        "I apologize, but I couldn't generate a response. The model may have returned empty content or encountered an issue during generation.",
      thinking: finalThinking || undefined,
      toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
      generationMetrics,
    });

    // Clear the cancellation flag and generation state
    await ctx.runMutation(api.conversations.clearCancellation, {
      conversationId: args.conversationId,
    });

    // Clear generation state to indicate completion
    await ctx.runMutation(api.conversations.setGenerationState, {
      conversationId: args.conversationId,
      isGenerating: false,
    });

    // Update usage if using built-in keys
    if (!usingUserKey) {
      // Deduct credits based on actual token usage
      const actualInputTokens = generationMetrics.promptTokens || 0;
      const actualOutputTokens = generationMetrics.completionTokens || 0;

      if (actualInputTokens > 0 || actualOutputTokens > 0) {
        try {
          await ctx.runMutation(api.usage.deductCredits, {
            model,
            inputTokens: actualInputTokens,
            outputTokens: actualOutputTokens,
          });
        } catch (creditError) {
          console.warn("Failed to deduct credits:", creditError);
          // Don't fail the whole request for credit deduction errors
        }
      }
    }

    return {
      messageId,
      content: finalContent,
      usingUserKey,
      generationMetrics,
    };
  } catch (error) {
    // Clear the cancellation check interval
    if (cancellationCheckInterval) {
      clearInterval(cancellationCheckInterval);
      cancellationCheckInterval = null;
    }

    // Clear timeout if it exists
    if (abortController) {
      abortController.abort();
    }

    let errorMessage: string;

    // Handle timeout errors specifically
    if (error instanceof Error && error.message.includes("timeout")) {
      errorMessage = `The ${provider} model (${model}) timed out. This can happen with complex requests or when the model is under heavy load. Please try again or consider using a different model.`;
    } else if (error instanceof Error && error.message.includes("aborted")) {
      errorMessage = `Request was cancelled: ${error.message}`;
    } else {
      errorMessage = getUserFriendlyErrorMessage(error, provider, usingUserKey);
    }

    console.error(`Generation error for ${provider}/${model}:`, error);

    // Clear generation state on error
    try {
      await ctx.runMutation(api.conversations.clearCancellation, {
        conversationId: args.conversationId,
      });
      await ctx.runMutation(api.conversations.setGenerationState, {
        conversationId: args.conversationId,
        isGenerating: false,
      });
    } catch (clearError) {
      // Ignore clearance errors
    }

    // Update the existing message with error information
    if (messageId) {
      try {
        await ctx.runMutation(api.messages.update, {
          messageId,
          content: errorMessage,
          generationMetrics: { provider, model, generationTimeMs: 0 },
          isError: true,
        });
      } catch (updateError) {
        // If updating fails, create a new message as fallback
        await ctx.runMutation(api.messages.add, {
          conversationId: args.conversationId,
          branchId: branchId,
          role: "assistant",
          content: errorMessage,
          generationMetrics: { provider, model, generationTimeMs: 0 },
          isError: true,
        });
      }
    } else {
      // If messageId is null, create a new message
      await ctx.runMutation(api.messages.add, {
        conversationId: args.conversationId,
        branchId: branchId,
        role: "assistant",
        content: errorMessage,
        generationMetrics: { provider, model, generationTimeMs: 0 },
        isError: true,
      });
    }

    return {
      messageId: messageId ?? undefined,
      error: errorMessage,
      usingUserKey,
    };
  }
}
